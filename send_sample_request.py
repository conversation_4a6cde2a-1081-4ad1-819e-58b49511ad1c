#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Send Sample Payment Request
===========================

Simple script to send the exact payment request as specified.
"""

import requests
import json

# API Configuration
API_URL = "http://localhost:5000/process-payment"

# The exact request data as specified
REQUEST_DATA = {
    "id": 532523,
    "amount": "24000",
    "number": "08632565",
    "code": None,
    "product": 50,
    "type": "2 MB",
    "date": "2025-08-07T08:22:28.000000Z"
}

def send_request():
    """Send the payment request"""
    try:
        print("🚀 إرسال طلب الدفع...")
        print("="*50)
        print("📋 بيانات الطلب:")
        print(json.dumps(REQUEST_DATA, indent=2, ensure_ascii=False))
        print("="*50)
        
        # Send POST request
        response = requests.post(
            API_URL,
            json=REQUEST_DATA,
            headers={
                "Content-Type": "application/json",
                "Accept": "application/json"
            },
            timeout=300  # 5 minutes timeout
        )
        
        print(f"📡 حالة الاستجابة: {response.status_code}")
        print("="*50)
        
        # Parse and display response
        if response.headers.get('content-type', '').startswith('application/json'):
            result = response.json()
            print("📄 استجابة الخادم:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # Display key information
            if 'status' in result:
                status_messages = {
                    1: "✅ تم الدفع بنجاح",
                    2: "❌ الرقم غير موجود في النظام",
                    3: "❌ فشل في عملية الدفع",
                    4: "❌ المشترك عليه دين"
                }
                status = result['status']
                print(f"\n🎯 نتيجة العملية: {status_messages.get(status, f'حالة غير معروفة ({status})')}")
                
                if 'company_name' in result:
                    print(f"🏢 الشركة: {result['company_name']}")
                
                if 'execution_time' in result:
                    print(f"⏱️ وقت التنفيذ: {result['execution_time']} ميلي ثانية")
        else:
            print("❌ استجابة غير صحيحة من الخادم:")
            print(response.text)
        
        return response.status_code == 200
        
    except requests.exceptions.ConnectionError:
        print("❌ فشل في الاتصال. تأكد من تشغيل الخادم على http://localhost:5000")
        return False
    except requests.exceptions.Timeout:
        print("⏰ انتهت مهلة الطلب (5 دقائق)")
        return False
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")
        return False

if __name__ == "__main__":
    print("📤 مرسل طلب الدفع النموذجي")
    print("="*50)
    
    success = send_request()
    
    if success:
        print("\n🎉 تم إرسال الطلب بنجاح!")
    else:
        print("\n💥 فشل في إرسال الطلب!")
    
    input("\nاضغط Enter للخروج...")
