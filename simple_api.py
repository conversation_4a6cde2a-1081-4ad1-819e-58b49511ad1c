#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Payment API Test
"""

from flask import Flask, request, jsonify
from datetime import datetime, timezone
import time

app = Flask(__name__)

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "message": "Payment API is running"
    })

@app.route('/test', methods=['POST'])
def test_endpoint():
    """Test endpoint"""
    data = request.get_json()
    return jsonify({
        "status": "success",
        "received_data": data,
        "timestamp": datetime.now(timezone.utc).isoformat()
    })

if __name__ == '__main__':
    print("🚀 Starting Simple Payment API...")
    print("🌐 Available endpoints:")
    print("   GET  /health - Health check")
    print("   POST /test   - Test endpoint")
    print(f"\n🔗 Server starting on http://localhost:5000")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
