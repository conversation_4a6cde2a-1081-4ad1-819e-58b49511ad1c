#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Payment API Test Script
=======================

Script to test the payment processing API with sample requests.
"""

import requests
import json
import time
from datetime import datetime

# API base URL
BASE_URL = "http://localhost:5000"

def test_health_check():
    """Test the health check endpoint"""
    print("🔍 Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print("✅ Health check passed")
            print(f"   Status: {data['status']}")
            print(f"   Supported products: {data['supported_products']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_companies_list():
    """Test the companies list endpoint"""
    print("\n🏢 Testing companies list...")
    try:
        response = requests.get(f"{BASE_URL}/companies")
        if response.status_code == 200:
            data = response.json()
            print("✅ Companies list retrieved")
            print(f"   Total companies: {data['total']}")
            for company in data['companies']:
                print(f"   {company['product_id']}: {company['name']} ({company['name_en']})")
            return True
        else:
            print(f"❌ Companies list failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Companies list error: {e}")
        return False

def test_payment_processing():
    """Test payment processing with sample data"""
    print("\n💳 Testing payment processing...")
    
    # Sample test data for different companies
    test_cases = [
        {
            "name": "Sawa Payment",
            "data": {
                "id": 123456,
                "amount": "5000",
                "number": "0987654321",
                "code": None,
                "product": 29,  # Corrected Sawa product ID
                "type": "1 GB",
                "date": datetime.now().isoformat() + "Z"
            }
        },
        {
            "name": "LEMA Payment",
            "data": {
                "id": 123457,
                "amount": "10000",
                "number": "0912345678",
                "code": None,
                "product": 49,  # Corrected LEMA product ID
                "type": "2 GB",
                "date": datetime.now().isoformat() + "Z"
            }
        },
        {
            "name": "INET Payment",
            "data": {
                "id": 123458,
                "amount": "15000",
                "number": "0933456789",
                "code": None,
                "product": 47,  # Corrected INET product ID
                "type": "3 GB",
                "date": datetime.now().isoformat() + "Z"
            }
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n🔄 Testing {test_case['name']}...")
        try:
            start_time = time.time()
            response = requests.post(
                f"{BASE_URL}/process-payment",
                json=test_case['data'],
                timeout=300  # 5 minutes timeout
            )
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ {test_case['name']} completed")
                print(f"   Status: {data['status']} - {data['message']}")
                print(f"   Company: {data['company_name']}")
                print(f"   Execution time: {data['execution_time']}ms")
                print(f"   Total request time: {(end_time - start_time)*1000:.2f}ms")
                results.append({"test": test_case['name'], "success": True, "data": data})
            else:
                print(f"❌ {test_case['name']} failed: {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data.get('message', 'Unknown error')}")
                    results.append({"test": test_case['name'], "success": False, "error": error_data})
                except:
                    print(f"   Raw response: {response.text}")
                    results.append({"test": test_case['name'], "success": False, "error": response.text})
                    
        except requests.exceptions.Timeout:
            print(f"⏰ {test_case['name']} timed out (5 minutes)")
            results.append({"test": test_case['name'], "success": False, "error": "Timeout"})
        except Exception as e:
            print(f"❌ {test_case['name']} error: {e}")
            results.append({"test": test_case['name'], "success": False, "error": str(e)})
    
    return results

def test_invalid_requests():
    """Test API with invalid requests"""
    print("\n🚫 Testing invalid requests...")
    
    invalid_cases = [
        {
            "name": "Missing required field",
            "data": {
                "id": 123,
                "amount": "1000"
                # Missing number, product, type
            }
        },
        {
            "name": "Invalid product ID",
            "data": {
                "id": 123,
                "amount": "1000",
                "number": "0987654321",
                "product": 999,  # Invalid product ID
                "type": "1 GB"
            }
        },
        {
            "name": "Invalid amount",
            "data": {
                "id": 123,
                "amount": "invalid",  # Invalid amount
                "number": "0987654321", 
                "product": 40,
                "type": "1 GB"
            }
        }
    ]
    
    for test_case in invalid_cases:
        print(f"\n🔄 Testing {test_case['name']}...")
        try:
            response = requests.post(
                f"{BASE_URL}/process-payment",
                json=test_case['data']
            )
            
            if response.status_code == 400:
                data = response.json()
                print(f"✅ {test_case['name']} correctly rejected")
                print(f"   Error: {data['message']}")
            else:
                print(f"❌ {test_case['name']} should have been rejected but got: {response.status_code}")
                
        except Exception as e:
            print(f"❌ {test_case['name']} error: {e}")

def main():
    """Run all tests"""
    print("🚀 Starting Payment API Tests")
    print("="*50)
    
    # Test basic endpoints
    health_ok = test_health_check()
    companies_ok = test_companies_list()
    
    if not health_ok:
        print("\n❌ API is not healthy. Stopping tests.")
        return
    
    # Test invalid requests
    test_invalid_requests()
    
    # Test payment processing (commented out by default as it requires actual payment processing)
    print("\n⚠️  Payment processing tests are available but commented out.")
    print("   Uncomment the line below to test actual payment processing:")
    print("   # results = test_payment_processing()")
    
    # Uncomment the next line to test actual payment processing
    # results = test_payment_processing()
    
    print("\n✅ API tests completed!")

if __name__ == "__main__":
    main()
