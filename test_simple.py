#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple API Test
"""

try:
    print("Testing imports...")
    
    from flask import Flask
    print("✅ Flask imported")
    
    from payment_processors import get_payment_processor, get_supported_products
    print("✅ Payment processors imported")
    
    app = Flask(__name__)
    print("✅ Flask app created")
    
    supported = get_supported_products()
    print(f"✅ Supported products: {supported}")
    
    processor = get_payment_processor(40)
    print(f"✅ Got processor for product 40: {processor}")
    
    print("\n🎉 All tests passed! API should work.")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
