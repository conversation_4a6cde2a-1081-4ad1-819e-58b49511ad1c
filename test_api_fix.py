#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test API Fix for Status and Message Consistency
===============================================

Test script to verify that API responses match terminal output
"""

from payment_processors import process_linet_payment, process_bitakat_payment, process_lema_payment

def test_linet_debt_case():
    """Test Linet payment with debt case"""
    print("Testing Linet payment with debt case...")
    
    # Create test order that should trigger debt case
    order = {
        "id": "587866",
        "phone_number": "2323194",
        "amount": 1,
        "type": "1 MB",
        "product": 46
    }
    
    try:
        success, status, message = process_linet_payment(order)
        print(f"Result: success={success}, status={status}, message='{message}'")
        
        # Expected: status=4, message="المبلغ المطلوب XXXX"
        if status == 4 and "المبلغ المطلوب" in message:
            print("✅ Linet test PASSED - Status and message are correct")
        else:
            print(f"❌ Linet test FAILED - Expected status=4 with debt message, got status={status}, message='{message}'")
            
    except Exception as e:
        print(f"❌ Linet test ERROR: {e}")

def test_bitakat_invalid_number():
    """Test Bitakat payment with invalid number"""
    print("\nTesting Bitakat payment with invalid number...")
    
    # Create test order that should trigger invalid number case
    order = {
        "id": "532523",
        "phone_number": "08632565",
        "amount": 24000,
        "type": "2 MB",
        "product": 50
    }
    
    try:
        success, status, message = process_bitakat_payment(order)
        print(f"Result: success={success}, status={status}, message='{message}'")
        
        # Expected: status=2, message="خطأ الرقم أو الشركة"
        if status == 2 and message == "خطأ الرقم أو الشركة":
            print("✅ Bitakat test PASSED - Status and message are correct")
        else:
            print(f"❌ Bitakat test FAILED - Expected status=2 with 'خطأ الرقم أو الشركة', got status={status}, message='{message}'")
            
    except Exception as e:
        print(f"❌ Bitakat test ERROR: {e}")

def test_lema_case():
    """Test LEMA payment"""
    print("\nTesting LEMA payment...")
    
    # Create test order
    order = {
        "id": "123456",
        "phone_number": "1234567",
        "amount": 1000,
        "type": "1 MB",
        "product": 49
    }
    
    try:
        success, status, message = process_lema_payment(order)
        print(f"Result: success={success}, status={status}, message='{message}'")
        print("✅ LEMA test completed - Check if status and message match terminal output")
            
    except Exception as e:
        print(f"❌ LEMA test ERROR: {e}")

if __name__ == "__main__":
    print("🧪 Testing API Status and Message Consistency")
    print("=" * 50)
    
    # Note: These tests will actually try to connect to the payment systems
    # In a real scenario, you might want to mock the network calls
    
    test_linet_debt_case()
    test_bitakat_invalid_number()
    test_lema_case()
    
    print("\n" + "=" * 50)
    print("🏁 Testing completed!")
    print("\nNote: The actual results depend on the real payment systems.")
    print("The important thing is that the API returns the same status and message")
    print("that would be displayed in the terminal.")
