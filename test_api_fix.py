#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test API Fix for Status and Message Consistency
===============================================

Test script to verify that API responses match terminal output
"""

from payment_processors import (
    process_sawa_payment, process_syriatel_payment, process_bitakat_payment,
    process_linet_payment, process_lema_payment, process_mts_payment,
    process_inet_payment, process_takamol_payment
)

def test_linet_debt_case():
    """Test Linet payment with debt case"""
    print("Testing Linet payment with debt case...")
    
    # Create test order that should trigger debt case
    order = {
        "id": "587866",
        "phone_number": "2323194",
        "amount": 1,
        "type": "1 MB",
        "product": 46
    }
    
    try:
        success, status, message = process_linet_payment(order)
        print(f"Result: success={success}, status={status}, message='{message}'")
        
        # Expected: status=4, message="المبلغ المطلوب XXXX"
        if status == 4 and "المبلغ المطلوب" in message:
            print("✅ Linet test PASSED - Status and message are correct")
        else:
            print(f"❌ Linet test FAILED - Expected status=4 with debt message, got status={status}, message='{message}'")
            
    except Exception as e:
        print(f"❌ Linet test ERROR: {e}")

def test_bitakat_invalid_number():
    """Test Bitakat payment with invalid number"""
    print("\nTesting Bitakat payment with invalid number...")
    
    # Create test order that should trigger invalid number case
    order = {
        "id": "532523",
        "phone_number": "08632565",
        "amount": 24000,
        "type": "2 MB",
        "product": 50
    }
    
    try:
        success, status, message = process_bitakat_payment(order)
        print(f"Result: success={success}, status={status}, message='{message}'")
        
        # Expected: status=2, message="خطأ الرقم أو الشركة"
        if status == 2 and message == "خطأ الرقم أو الشركة":
            print("✅ Bitakat test PASSED - Status and message are correct")
        else:
            print(f"❌ Bitakat test FAILED - Expected status=2 with 'خطأ الرقم أو الشركة', got status={status}, message='{message}'")
            
    except Exception as e:
        print(f"❌ Bitakat test ERROR: {e}")

def test_sawa_case():
    """Test Sawa payment"""
    print("\nTesting Sawa payment...")

    # Create test order
    order = {
        "id": "123456",
        "phone_number": "1234567",
        "amount": 1000,
        "type": "1 MB",
        "product": 29
    }

    try:
        success, status, message = process_sawa_payment(order)
        print(f"Result: success={success}, status={status}, message='{message}'")
        print("✅ Sawa test completed - Check if status and message match terminal output")

    except Exception as e:
        print(f"❌ Sawa test ERROR: {e}")

def test_syriatel_case():
    """Test Syriatel payment"""
    print("\nTesting Syriatel payment...")

    # Create test order
    order = {
        "id": "123456",
        "phone_number": "1234567",
        "amount": 1000,
        "type": "1 MB",
        "product": 25
    }

    try:
        success, status, message = process_syriatel_payment(order)
        print(f"Result: success={success}, status={status}, message='{message}'")
        print("✅ Syriatel test completed - Check if status and message match terminal output")

    except Exception as e:
        print(f"❌ Syriatel test ERROR: {e}")

def test_all_companies():
    """Test all companies to verify API wrapper consistency"""
    print("\n🧪 Testing ALL Companies for API Consistency")
    print("=" * 60)

    companies = [
        ("Sawa", 29, process_sawa_payment),
        ("Syriatel", 25, process_syriatel_payment),
        ("Bitakat", 50, process_bitakat_payment),
        ("Linet", 46, process_linet_payment),
        ("LEMA", 49, process_lema_payment),
        ("MTS", 48, process_mts_payment),
        ("INET", 47, process_inet_payment),
        ("Takamol", 51, process_takamol_payment)
    ]

    for company_name, product_id, processor_func in companies:
        print(f"\n📋 Testing {company_name} (Product ID: {product_id})")
        print("-" * 40)

        # Create test order
        order = {
            "id": "TEST123",
            "phone_number": "1234567",
            "amount": 1000,
            "type": "1 MB",
            "product": product_id
        }

        try:
            success, status, message = processor_func(order)
            print(f"✅ {company_name}: success={success}, status={status}")
            print(f"   Message: '{message}'")

            # Verify return format
            if isinstance(status, int) and isinstance(message, str):
                print(f"   ✅ Return format is correct")
            else:
                print(f"   ❌ Return format is incorrect - status type: {type(status)}, message type: {type(message)}")

        except Exception as e:
            print(f"❌ {company_name} ERROR: {e}")

if __name__ == "__main__":
    print("🧪 Testing API Status and Message Consistency")
    print("=" * 50)

    # Note: These tests will actually try to connect to the payment systems
    # In a real scenario, you might want to mock the network calls

    test_linet_debt_case()
    test_bitakat_invalid_number()
    test_sawa_case()
    test_syriatel_case()

    # Test all companies for consistency
    test_all_companies()

    print("\n" + "=" * 50)
    print("🏁 Testing completed!")
    print("\nNote: The actual results depend on the real payment systems.")
    print("The important thing is that ALL companies now return the same")
    print("status and message format that matches the terminal output.")
