@echo off
chcp 65001 >nul
title معالجات الدفع - طلب واحد

:menu
cls
echo ===============================================
echo           معالجات الدفع - طلب واحد
echo ===============================================
echo.
echo اختر المعالج المطلوب:
echo.
echo 1. Sawa Payment Processor
echo 2. LEMA Payment Processor  
echo 3. Linet Payment Processor
echo 4. Takamol Payment Processor
echo 5. خروج
echo.
set /p choice="أدخل اختيارك (1-5): "

if "%choice%"=="1" goto sawa
if "%choice%"=="2" goto lema
if "%choice%"=="3" goto linet
if "%choice%"=="4" goto takamol
if "%choice%"=="5" goto exit
goto invalid

:sawa
cls
echo تشغيل معالج دفع Sawa...
echo.
python payToSawa.py
echo.
pause
goto menu

:lema
cls
echo تشغيل معالج دفع LEMA...
echo.
python payToLEMA.py
echo.
pause
goto menu

:linet
cls
echo تشغيل معالج دفع Linet...
echo.
python payToLinet.py
echo.
pause
goto menu

:takamol
cls
echo تشغيل معالج دفع Takamol...
echo.
python payToTakamol.py
echo.
pause
goto menu

:invalid
cls
echo اختيار غير صحيح! يرجى المحاولة مرة أخرى.
echo.
pause
goto menu

:exit
echo شكراً لاستخدام معالجات الدفع!
pause
exit
