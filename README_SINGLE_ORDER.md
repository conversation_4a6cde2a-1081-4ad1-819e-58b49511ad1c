# دليل استخدام معالجات الدفع - طلب واحد

## نظرة عامة

تم تعديل جميع ملفات معالجة الدفع لتعمل مع طلب واحد من المستخدم بدلاً من جلب الطلبات من API. هذا يسمح بالاختبار والاستخدام المباشر دون الحاجة لإعداد API.

## الملفات المعدلة

### 1. payToSawa.py
- **الوصف**: معالج دفع Sawa للطلب الواحد
- **كيفية الاستخدام**: 
  ```bash
  python payToSawa.py
  ```
- **المدخلات المطلوبة**:
  - رقم الطلب (Order ID)
  - رقم الهاتف
  - المبلغ

### 2. payToLEMA.py
- **الوصف**: معالج دفع LEMA للطلب الواحد
- **كيفية الاستخدام**:
  ```bash
  python payToLEMA.py
  ```
- **المدخلات المطلوبة**:
  - رقم الطلب (Order ID)
  - رقم الهاتف
  - المبلغ

### 3. payToLinet.py
- **الوصف**: معالج دفع Linet للطلب الواحد
- **كيفية الاستخدام**:
  ```bash
  python payToLinet.py
  ```
- **المدخلات المطلوبة**:
  - رقم الطلب (Order ID)
  - رقم الهاتف
  - المبلغ

### 4. payToTakamol.py
- **الوصف**: معالج دفع Takamol للطلب الواحد
- **كيفية الاستخدام**:
  ```bash
  python payToTakamol.py
  ```
- **المدخلات المطلوبة**:
  - رقم الطلب (Order ID)
  - رقم الهاتف
  - المبلغ

### 5. payToINET.py
- **الوصف**: معالج دفع INET للطلب الواحد
- **كيفية الاستخدام**:
  ```bash
  python payToINET.py
  ```
- **المدخلات المطلوبة**:
  - رقم الطلب (Order ID)
  - رقم الهاتف
  - المبلغ

### 6. payToSyriatel.py
- **الوصف**: معالج دفع Syriatel للطلب الواحد
- **كيفية الاستخدام**:
  ```bash
  python payToSyriatel.py
  ```
- **المدخلات المطلوبة**:
  - رقم الطلب (Order ID)
  - رقم الهاتف
  - المبلغ

### 7. payToMTS.py
- **الوصف**: معالج دفع MTS للطلب الواحد
- **كيفية الاستخدام**:
  ```bash
  python payToMTS.py
  ```
- **المدخلات المطلوبة**:
  - رقم الطلب (Order ID)
  - رقم الهاتف
  - المبلغ

### 8. payToBitakat.py
- **الوصف**: معالج دفع Bitakat للطلب الواحد
- **كيفية الاستخدام**:
  ```bash
  python payToBitakat.py
  ```
- **المدخلات المطلوبة**:
  - رقم الطلب (Order ID)
  - رقم الهاتف
  - المبلغ

## التغييرات المطبقة

### 1. إزالة اتصال API
- تم تعطيل جلب الطلبات من API
- تم تعطيل إرسال النتائج إلى API
- تم الاحتفاظ بالكود المعطل للمرجعية

### 2. إدخال المستخدم
- تم إضافة واجهة لإدخال بيانات الطلب من المستخدم
- يطلب من المستخدم إدخال: رقم الطلب، رقم الهاتف، والمبلغ
- يتم التحقق من صحة البيانات المدخلة

### 3. عرض النتائج
- تم استبدال دوال `send_status_to_api_*` بدوال `display_payment_result_*`
- يتم عرض نتيجة المعالجة على الشاشة بدلاً من إرسالها للAPI
- يتم عرض رسائل مفصلة باللغة العربية

### 4. إزالة حفظ الطلبات المعالجة
- تم إزالة حفظ الطلبات في ملفات نصية
- تم إزالة فحص الطلبات المكررة المعتمد على الملفات

### 5. إزالة نظام الترخيص
- تم حذف ملف `license_manager.py` بالكامل
- تم إزالة جميع عمليات التحقق من الترخيص والأجهزة المصرح بها
- تم إزالة الاتصال بخادم التحقق من الترخيص
- الآن تعمل جميع الملفات في وضع مستقل بدون قيود ترخيص

### 6. إزالة نظام السجلات وملفات HTML
- تم إزالة جميع عمليات إنشاء ملفات السجلات (.txt, .log)
- تم إزالة حفظ استجابات تسجيل الدخول في ملفات HTML
- تم إزالة حفظ جميع ردود API في ملفات HTML:
  - `*_dashboard_balance.html`
  - `*_search_response_*.html`
  - `*_payment_form_*.html`
  - `*_first_confirmation_*.html`
  - `*_final_confirmation_*.html`
- تم إزالة ملفات تتبع الطلبات المعالجة
- الآن يتم عرض السجلات على الشاشة فقط دون حفظها في ملفات
- تم إزالة استيراد `threading` المستخدم لحماية ملفات السجلات
- **لا يتم إنشاء أي ملفات على الإطلاق أثناء التشغيل**

## رموز الحالة

- **1**: تم الدفع بنجاح ✅
- **2**: الرقم غير موجود في النظام ❌
- **3**: فشل في عملية الدفع ❌
- **4**: المشترك عليه دين ❌

## مثال على الاستخدام

```bash
$ python payToSawa.py

=== معالج دفع Sawa - طلب واحد ===
يرجى إدخال بيانات الطلب:
رقم الطلب (Order ID): 12345
رقم الهاتف: 0987654321
المبلغ: 5000

📋 معلومات الطلب:
رقم الطلب: 12345
رقم الهاتف: 0987654321
المبلغ: 5000.0

🔄 بدء معالجة الطلب...

==================================================
📋 نتيجة معالجة الطلب - Sawa
==================================================
رقم الطلب: 12345
حالة الطلب: 1
الوصف: ✅ تم الدفع بنجاح
==================================================

✅ تم معالجة الطلب 12345 بنجاح
```

## ملاحظات مهمة

1. **الاعتمادات**: تأكد من وجود جميع المكتبات المطلوبة
2. **الشبكة**: تحتاج اتصال إنترنت للوصول لأنظمة الدفع
3. **المتصفح**: ملف Sawa يحتاج Chrome/Chromium مثبت
4. **وضع مستقل**: تعمل جميع الملفات بدون قيود ترخيص أو تحقق من الخادم

## استكشاف الأخطاء

### خطأ في البيانات المدخلة
- تأكد من إدخال جميع البيانات المطلوبة
- تأكد من أن المبلغ رقم صحيح

### خطأ في الاتصال
- تحقق من اتصال الإنترنت
- تحقق من صحة بيانات تسجيل الدخول

### خطأ في المتصفح (Sawa فقط)
- تأكد من تثبيت Chrome
- تحقق من إصدار ChromeDriver

## الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق التطوير.
