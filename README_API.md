# Payment Processing API

## نظام API لمعالجة الدفعات

### 📋 الوصف
API يستقبل طلبات الدفع ويوجهها للمعالج المناسب حسب الشركة (product ID).

### 🚀 كيفية التشغيل

#### 1. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

#### 2. تشغيل الخادم
```bash
python payment_api.py
```

الخادم سيعمل على: `http://localhost:5000`

### 📡 نقاط النهاية (Endpoints)

#### 1. معالجة الدفع
**POST** `/process-payment`

**طلب الإدخال:**
```json
{
    "id": 586316,
    "amount": "24000",
    "number": "7530490",
    "code": null,
    "product": 44,
    "type": "2 MB",
    "date": "2025-08-07T08:22:28.000000Z"
}
```

**رد الخادم:**
```json
{
    "status": 1,
    "message": "تم الدفع بنجاح",
    "request_data": {
        "id": 586316,
        "amount": "24000",
        "number": "7530490",
        "product": 44,
        "type": "2 MB"
    },
    "completion_time": "2025-08-07T08:25:30.123456Z",
    "execution_time": 182.5,
    "company_name": "إنت"
}
```

#### 2. فحص الحالة
**GET** `/health`

**رد الخادم:**
```json
{
    "status": "healthy",
    "timestamp": "2025-08-07T08:22:28.123456Z",
    "supported_products": [40, 41, 42, 43, 44, 45, 46, 50]
}
```

#### 3. قائمة الشركات
**GET** `/companies`

**رد الخادم:**
```json
{
    "companies": [
        {
            "product_id": 40,
            "name": "سوا",
            "name_en": "Sawa"
        },
        {
            "product_id": 41,
            "name": "ليما",
            "name_en": "LEMA"
        }
    ],
    "total": 8
}
```

### 🏢 الشركات المدعومة

| Product ID | الاسم العربي | الاسم الإنجليزي |
|------------|-------------|-----------------|
| 25 | سيرياتيل | Syriatel |
| 29 | سوا | Sawa |
| 46 | لاينت | Linet |
| 47 | إنت | INET |
| 48 | إم تي إس | MTS |
| 49 | ليما | LEMA |
| 50 | بطاقات | Bitakat |
| 51 | تكامل | Takamol |

### 📊 رموز الحالة

| الرمز | الوصف |
|------|-------|
| 1 | تم الدفع بنجاح |
| 2 | الرقم غير موجود في النظام |
| 3 | فشل في عملية الدفع |
| 4 | المشترك عليه دين |

### 🔧 البيانات المطلوبة

من الطلب الوارد، يتم استخدام البيانات التالية فقط:
- **number**: رقم الهاتف
- **type**: نوع الباقة
- **amount**: المبلغ

### ⚡ مثال على الاستخدام

#### باستخدام curl:
```bash
curl -X POST http://localhost:5000/process-payment \
  -H "Content-Type: application/json" \
  -d '{
    "id": 123456,
    "amount": "5000",
    "number": "0987654321",
    "product": 40,
    "type": "1 GB"
  }'
```

#### باستخدام Python:
```python
import requests

url = "http://localhost:5000/process-payment"
data = {
    "id": 123456,
    "amount": "5000",
    "number": "0987654321",
    "product": 40,
    "type": "1 GB"
}

response = requests.post(url, json=data)
result = response.json()
print(result)
```

### 🛡️ معالجة الأخطاء

- **400 Bad Request**: بيانات غير صحيحة أو ناقصة
- **500 Internal Server Error**: خطأ في الخادم أو معالجة الدفع

### 📝 ملاحظات

- جميع الأوقات بتوقيت UTC
- زمن التنفيذ بالميلي ثانية
- يتم عرض السجلات في وحدة التحكم فقط
- لا يتم حفظ أي ملفات أثناء التشغيل
